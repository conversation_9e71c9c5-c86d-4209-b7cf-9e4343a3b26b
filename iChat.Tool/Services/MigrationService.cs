using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using iChat.Shared.Data;
using Supabase;

namespace iChat.Tool.Services;

public class MigrationService : IMigrationService
{
    private readonly ILogger<MigrationService> _logger;
    private readonly IConfiguration _configuration;
    private readonly ApplicationDbContext _dbContext;

    public MigrationService(ILogger<MigrationService> logger, IConfiguration configuration, ApplicationDbContext dbContext)
    {
        _logger = logger;
        _configuration = configuration;
        _dbContext = dbContext;
    }

    public async Task<bool> CheckConnectionAsync()
    {
        try
        {
            // Check EF Core database connection
            var canConnect = await _dbContext.Database.CanConnectAsync();
            if (canConnect)
            {
                _logger.LogInformation("Successfully connected to database via Entity Framework");
                return true;
            }
            else
            {
                _logger.LogError("Cannot connect to database via Entity Framework");
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to connect to database");
            return false;
        }
    }

    public async Task RunMigrationsAsync()
    {
        _logger.LogInformation("Starting database migrations...");

        var connected = await CheckConnectionAsync();
        if (!connected)
        {
            throw new InvalidOperationException("Cannot connect to database. Please check your configuration.");
        }

        try
        {
            _logger.LogInformation("Applying Entity Framework migrations...");
            
            // Apply any pending migrations
            var pendingMigrations = await _dbContext.Database.GetPendingMigrationsAsync();
            if (pendingMigrations.Any())
            {
                _logger.LogInformation($"Applying {pendingMigrations.Count()} pending migrations...");
                foreach (var migration in pendingMigrations)
                {
                    _logger.LogInformation($"  - {migration}");
                }
                
                await _dbContext.Database.MigrateAsync();
                _logger.LogInformation("All migrations applied successfully");
            }
            else
            {
                _logger.LogInformation("No pending migrations found. Database is up to date.");
            }
            
            _logger.LogInformation("Database migrations completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Migration failed");
            throw;
        }
    }
}
