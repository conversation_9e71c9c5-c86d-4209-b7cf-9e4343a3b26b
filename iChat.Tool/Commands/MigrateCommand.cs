using Spectre.Console;
using Spectre.Console.Cli;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using iChat.Tool.Services;
using System.ComponentModel;

namespace iChat.Tool.Commands;

public class MigrateSettings : CommandSettings
{
    [CommandOption("--check")]
    [Description("Check database connection without running migrations")]
    public bool CheckOnly { get; set; }
}

public class MigrateCommand : AsyncCommand<MigrateSettings>
{
    private readonly IServiceProvider _serviceProvider;
    private readonly IConfiguration _configuration;

    public MigrateCommand(IServiceProvider serviceProvider, IConfiguration configuration)
    {
        _serviceProvider = serviceProvider;
        _configuration = configuration;
    }

    public override async Task<int> ExecuteAsync(CommandContext context, MigrateSettings settings)
    {
        using var scope = _serviceProvider.CreateScope();
        var migrationService = scope.ServiceProvider.GetRequiredService<IMigrationService>();
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<MigrateCommand>>();

        try
        {
            if (settings.CheckOnly)
            {
                logger.LogInformation("Checking database connection...");

                await AnsiConsole.Status()
                    .StartAsync("Checking database connection...", async ctx =>
                    {
                        ctx.Spinner(Spinner.Known.Dots);
                        ctx.SpinnerStyle(Style.Parse("green"));

                        var connected = await migrationService.CheckConnectionAsync();
                        if (connected)
                        {
                            AnsiConsole.MarkupLine("[green]✅ Database connection successful[/]");
                        }
                        else
                        {
                            AnsiConsole.MarkupLine("[red]❌ Database connection failed[/]");
                            return;
                        }
                    });

                return 0;
            }
            else
            {
                AnsiConsole.MarkupLine("[blue]Starting database migrations...[/]");

                await AnsiConsole.Progress()
                    .StartAsync(async ctx =>
                    {
                        var task = ctx.AddTask("[green]Running migrations[/]");

                        // Simulate progress during migration
                        task.StartTask();
                        await migrationService.RunMigrationsAsync();
                        task.Value = 100;
                    });

                AnsiConsole.MarkupLine("[green]✅ Migrations completed successfully[/]");
                return 0;
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Migration command failed");

            // Get the connection string that was used
            var connectionString = _configuration.GetConnectionString("DefaultConnection")
                ?? Environment.GetEnvironmentVariable("CONNECTION_STRING")
                ?? "Not found";

            AnsiConsole.MarkupLine($"[red]❌ Migration failed: {ex.Message}[/]");
            AnsiConsole.MarkupLine($"[yellow]Connection string used: {connectionString}[/]");
            return 1;
        }
    }
}
