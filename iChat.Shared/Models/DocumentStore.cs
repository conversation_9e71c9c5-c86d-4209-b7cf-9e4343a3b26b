using System.ComponentModel.DataAnnotations;

namespace iChat.Shared.Models;

public class DocumentStore
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public Guid CreatedBy { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    
    public virtual User CreatedByUser { get; set; } = null!;
    public virtual ICollection<Document> Documents { get; set; } = new List<Document>();
}
